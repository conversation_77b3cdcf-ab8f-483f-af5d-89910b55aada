import 'package:voji/models/base_model.dart';
import 'package:voji/models/enums.dart';

/// Represents an Ideabook, which is a collection of ideas
class Ideabook extends BaseModel {
  /// Short name of the ideabook, usually summarized by LLM (field 'n' in Firestore)
  final String name;

  /// Color of the ideabook for categorization (field 'c' in Firestore)
  final IdeabookColor color;

  /// Whether the ideabook is locked (field 'l' in Firestore)
  final bool isLocked;

  /// Creates a new Ideabook instance
  const Ideabook({
    required super.id,
    required this.name,
    this.color = IdeabookColor.none,
    this.isLocked = false,
    required super.createdAt,
    required super.updatedAt,
  });

  @override
  Ideabook copyWith({
    String? id,
    String? name,
    IdeabookColor? color,
    bool? isLocked,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Ideabook(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
      isLocked: isLocked ?? this.isLocked,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Ideabook{id: $id, name: $name, color: $color, isLocked: $isLocked}';
  }
}
