import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/audio/audio_recording_controller.dart';
import 'package:voji/services/firebase/firebase_providers.dart';
import 'package:voji/services/limits/app_limits_providers.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/services/firebase/firestore_listener_pool.dart';
import 'package:voji/models/models.dart';
import 'package:voji/ui/providers/ideabook_notification_provider.dart';
import 'package:voji/ui/providers/ideabook_recording_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/utils/logger.dart';

/// Controller for recording audio for new ideas
class IdeaRecordingController extends BaseAudioRecordingController {
  /// The ideabook ID for which the idea is being recorded
  final String ideabookId;

  /// Constructor
  IdeaRecordingController({required this.ideabookId});

  @override
  String get controllerName => 'IdeaRecordingController';

  @override
  Future<void> handleRecordingCompleted(WidgetRef ref, String filePath, Duration duration) async {
    Logger.debug('$controllerName: Recording completed for ideabook $ideabookId: $filePath, duration: ${duration.inSeconds}s');

    // Set the state to processing
    beginAudioProcessing(ref);

    try {
      // Transcribe the audio
      Logger.debug('$controllerName: Transcribing audio...');
      final llmService = safelyUseRef(ref, (r) => r.read(llmServiceProvider));
      if (llmService == null) {
        Logger.error('$controllerName: Could not access LLM service - widget may be disposed');
        return;
      }

      // Get the ideabook name from the Firestore service using listener pool
      String? ideabookName;
      try {
        final listenerPool = safelyUseRef(ref, (r) => r.read(firestoreListenerPoolProvider));
        if (listenerPool != null) {
          final ideabook = await listenerPool.getIdeabookStream(ideabookId).first;
          if (ideabook != null) {
            ideabookName = ideabook.name;
            Logger.debug('$controllerName: Using ideabook name: $ideabookName');
          }
        }
      } catch (e) {
        Logger.error('$controllerName: Error getting ideabook name', e);
      }

      final transcriptionResult = await llmService.transcribeAudio(
        filePath,
        useCase: TranscriptionUseCase.newIdea,
        ideabookName: ideabookName,
      );

      if (transcriptionResult.isSuccess && transcriptionResult.idea != null) {
        // Create a new idea with the transcription
        Logger.debug('$controllerName: Creating new idea with content: ${transcriptionResult.idea}');

        try {
          // Use Firestore service directly to create the idea
          final firestoreService = safelyUseRef(ref, (r) => r.read(firestoreServiceProvider));

          if (firestoreService == null) {
            Logger.error('$controllerName: Could not access Firestore service - widget may be disposed');
            return;
          }

          // Create the idea model
          final idea = Idea(
            id: '', // Will be set by Firestore
            content: transcriptionResult.idea!,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          final newIdea = await firestoreService.createIdea(ideabookId, idea);

          Logger.debug('$controllerName: New idea created with ID: ${newIdea.id}');

          // No need to explicitly refresh when using Firestore as it will update automatically via the listener

          // Show success notification with the short title if available
          final shortTitle = transcriptionResult.shortTitle;
          final notificationMessage = shortTitle != null
              ? 'Added $shortTitle'
              : 'New idea added successfully';
          showNotification(ref, notificationMessage);
        } catch (e) {
          Logger.error('$controllerName: Failed to create new idea', e);

          // Check if the error is due to the ideabook being full
          if (e.toString().contains('ideas reached')) {
            // Show specific error dialog for ideabook full
            _showIdeabookFullDialog(ref);
          } else {
            // Show generic error notification
            showNotification(ref, 'Failed to create new idea');
          }
        }
      } else {
        // Transcription failed, do NOT create a document to save Firestore writes
        Logger.error('$controllerName: Transcription failed: ${transcriptionResult.errorMessage}');

        // Show error notification
        showNotification(ref, 'Transcription failed - please try again');
      }
    } catch (e) {
      // Handle any unexpected errors
      Logger.error('$controllerName: Error processing recording', e);

      // Do NOT create a document on error to save Firestore writes
      // Show error notification
      showNotification(ref, 'Error processing recording - please try again');
    } finally {
      // Reset the processing state
      completeAudioProcessing(ref);

      // Exit recording mode
      safelyUseRef(ref, (r) {
        r.read(ideabookRecordingProvider.notifier).state = null;
      });
    }
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    Logger.debug('$controllerName: Recording cancelled for ideabook $ideabookId');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    Logger.error('$controllerName: Recording failed for ideabook $ideabookId: $errorMessage');

    // Show error notification
    showNotification(ref, 'Recording failed');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    Logger.error('$controllerName: Microphone permission denied for ideabook $ideabookId');

    // Show permission error notification
    showNotification(ref, 'Microphone permission required');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookRecordingProvider.notifier).state = null;
    });
  }

  @override
  void showNotification(WidgetRef ref, String message) {
    // Use the ideabook notification provider to show inline notifications
    safelyUseRef(ref, (r) {
      r.read(showIdeabookNotificationProvider)(
        ideabookId,
        message,
      );
    });
  }

  /// Show a dialog indicating that the ideabook is full
  Future<void> _showIdeabookFullDialog(WidgetRef ref) async {
    // Get the max ideas limit from the provider for logging purposes
    try {
      final future = safelyUseRef(ref, (r) => r.read(maxIdeasPerIdeabookProvider.future));
      if (future != null) {
        final maxIdeas = await future;
        Logger.debug('$controllerName: Ideabook full dialog shown (limit: $maxIdeas)');
      }
    } catch (e) {
      Logger.error('$controllerName: Failed to get max ideas limit for logging', e);
    }

    // Get the BuildContext from the current navigator
    final context = safelyUseRef(ref, (r) =>
      r.read(ideabookRecordingProvider) != null ?
      Navigator.of(r.context).context : null
    );

    if (context == null) {
      Logger.error('$controllerName: Could not show ideabook full dialog - no context available');
      return;
    }

    // Use a post-frame callback to show the dialog after the current frame is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: Text(
            'Ideabook Full! 🈵',
            style: VojiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            "Wow, you've packed this Ideabook with many amazing ideas! 🤩 Time for a new one 📖 to keep those brilliant thoughts flowing. Create a fresh Ideabook to add more! 💡",
            style: VojiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: VojiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'OK',
                style: VojiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
          ],
        ),
      );
    });
  }
}

/// Provider for the idea recording controller
final ideaRecordingControllerProvider = Provider.family<IdeaRecordingController, String>((ref, ideabookId) {
  return IdeaRecordingController(ideabookId: ideabookId);
});
