import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/audio/audio_providers.dart';
import 'package:voji/services/audio/audio_recording_controller.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/ui/providers/audio_providers.dart';
import 'package:voji/ui/providers/note_title_recording_provider.dart';
import 'package:voji/utils/logger.dart';

/// Controller for recording audio to append to a note title in edit mode
class NoteTitleRecordingController extends BaseAudioRecordingController {
  /// The note ID for which the recording is being made
  final String noteId;

  /// Callback to handle the transcribed text
  final Function(String transcribedText)? onTranscriptionComplete;

  /// Flag to track whether the callback has been called
  bool _callbackCalled = false;

  /// Constructor
  NoteTitleRecordingController({
    required this.noteId,
    this.onTranscriptionComplete,
  });

  @override
  String get controllerName => 'NoteTitleRecordingController';

  @override
  Future<void> handleRecordingCompleted(WidgetRef ref, String filePath, Duration duration) async {
    Logger.debug('$controllerName: Recording completed for note $noteId: $filePath, duration: ${duration.inSeconds}s');

    // Set the state to processing
    beginAudioProcessing(ref);

    try {
      // Transcribe the audio
      Logger.debug('$controllerName: Transcribing audio...');
      final llmService = safelyUseRef(ref, (r) => r.read(llmServiceProvider));
      if (llmService == null) {
        Logger.error('$controllerName: Could not access LLM service - widget may be disposed');
        return;
      }

      final transcriptionResult = await llmService.transcribeAudio(
        filePath,
        useCase: TranscriptionUseCase.newIdea,
      );

      if (transcriptionResult.isSuccess && transcriptionResult.idea != null) {
        // Get the transcribed text
        final transcribedText = transcriptionResult.idea!;
        Logger.debug('$controllerName: Transcription successful: $transcribedText');

        // Call the callback with the transcribed text only if it hasn't been called yet
        if (onTranscriptionComplete != null && !_callbackCalled) {
          _callbackCalled = true;
          Logger.debug('$controllerName: Calling onTranscriptionComplete callback');
          try {
            onTranscriptionComplete!(transcribedText);
          } catch (e) {
            Logger.error('$controllerName: Could not complete audio processing - widget may be disposed', e);
          }
        } else if (_callbackCalled) {
          Logger.debug('$controllerName: Skipping duplicate callback call');
        }
      } else {
        // Handle transcription failure
        Logger.error('$controllerName: Transcription failed: ${transcriptionResult.errorMessage}');
        showNotification(ref, 'Transcription failed');
      }
    } catch (e) {
      Logger.error('$controllerName: Error processing recording', e);
      showNotification(ref, 'Error processing recording');
    } finally {
      // Reset the processing state
      completeAudioProcessing(ref);

      // Exit recording mode
      safelyUseRef(ref, (r) {
        r.read(noteTitleRecordingProvider.notifier).state = null;
      });
    }
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    Logger.debug('$controllerName: Recording cancelled for note $noteId');

    // Reset callback flag
    _callbackCalled = false;

    // Reset the audio processing state to ensure waveform doesn't freeze
    safelyUseRef(ref, (r) {
      r.read(audioProcessingStateProvider.notifier).state = AudioProcessingState.idle;
    });

    // Reset the audio recording state to ensure clean state
    safelyUseRef(ref, (r) {
      final recordingStateNotifier = r.read(audioRecordingStateProvider.notifier);
      recordingStateNotifier.resetState();
    });

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(noteTitleRecordingProvider.notifier).state = null;
    });

    Logger.debug('$controllerName: Exited recording mode after cancellation');
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    Logger.error('$controllerName: Recording failed for note $noteId: $errorMessage');

    // Reset callback flag
    _callbackCalled = false;

    // Reset the audio processing state to ensure waveform doesn't freeze
    safelyUseRef(ref, (r) {
      r.read(audioProcessingStateProvider.notifier).state = AudioProcessingState.idle;
    });

    // Reset the audio recording state to ensure clean state
    safelyUseRef(ref, (r) {
      final recordingStateNotifier = r.read(audioRecordingStateProvider.notifier);
      recordingStateNotifier.resetState();
    });

    // Show error notification
    showNotification(ref, 'Recording failed');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(noteTitleRecordingProvider.notifier).state = null;
    });

    Logger.debug('$controllerName: Exited recording mode after failure');
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    Logger.error('$controllerName: Microphone permission denied for note $noteId');

    // Reset callback flag
    _callbackCalled = false;

    // Reset the audio processing state to ensure waveform doesn't freeze
    safelyUseRef(ref, (r) {
      r.read(audioProcessingStateProvider.notifier).state = AudioProcessingState.idle;
    });

    // Reset the audio recording state to ensure clean state
    safelyUseRef(ref, (r) {
      final recordingStateNotifier = r.read(audioRecordingStateProvider.notifier);
      recordingStateNotifier.resetState();
    });

    // Show permission error notification
    showNotification(ref, 'Microphone permission required');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(noteTitleRecordingProvider.notifier).state = null;
    });

    Logger.debug('$controllerName: Exited recording mode after permission denial');
  }

  @override
  void showNotification(WidgetRef ref, String message) {
    // Show a snackbar notification
    safelyUseRef(ref, (r) {
      // Use a simple logger for now since we don't have a specific notification provider for notes
      Logger.debug('$controllerName: $message');
    });
  }
}

/// Parameters for the note title recording controller
class NoteTitleRecordingParams {
  /// The note ID
  final String noteId;

  /// Callback for when transcription is complete
  final Function(String transcribedText)? onTranscriptionComplete;

  /// Constructor
  const NoteTitleRecordingParams({
    required this.noteId,
    this.onTranscriptionComplete,
  });
}

/// Provider for the note title recording controller
final noteTitleRecordingControllerProvider = Provider.family<NoteTitleRecordingController, NoteTitleRecordingParams>((ref, params) {
  return NoteTitleRecordingController(
    noteId: params.noteId,
    onTranscriptionComplete: params.onTranscriptionComplete,
  );
});
