import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/chat/chat_rate_limiter.dart';
import 'package:voji/services/limits/app_limits_providers.dart';

/// Provider for the chat rate limiter
final chatRateLimiterProvider = FutureProvider<ChatRateLimiter>((ref) async {
  final rateLimits = await ref.watch(chatRateLimitsProvider.future);
  return ChatRateLimiter(rateLimits: rateLimits);
});

/// Provider for the current rate limit status
final chatRateLimitStatusProvider = StateProvider<RateLimitResult?>((ref) => null);

/// Provider for checking the rate limit status
final checkChatRateLimitProvider = FutureProvider<RateLimitResult>((ref) async {
  final rateLimiter = await ref.watch(chatRateLimiterProvider.future);
  final result = await rateLimiter.checkRateLimit();

  // Update the status provider
  ref.read(chatRateLimitStatusProvider.notifier).state = result;

  return result;
});
