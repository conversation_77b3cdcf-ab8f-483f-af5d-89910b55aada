import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/chat_rate_limit.dart';
import 'package:voji/services/limits/app_limits_service.dart';
import 'package:voji/services/limits/repository_limits_helper.dart';
import 'package:voji/services/remote_config/remote_config_providers.dart';
import 'package:voji/services/subscription/subscription_providers.dart';

/// Provider for the app limits service
final appLimitsServiceProvider = Provider<AppLimitsService>((ref) {
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return AppLimitsService(remoteConfigService);
});

/// Provider for the repository limits helper
final repositoryLimitsHelperProvider = Provider<RepositoryLimitsHelper>((ref) {
  return RepositoryLimitsHelper(ref.container);
});

/// Provider for the current user's maximum ideabooks
final maxIdeabooksProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getMaxIdeabooks(userTier: userTier);
});

/// Provider for the current user's maximum ideas per ideabook
final maxIdeasPerIdeabookProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getMaxIdeasPerIdeabook(userTier: userTier);
});

/// Provider for the current user's maximum notes per ideabook
final maxNotesPerIdeabookProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getMaxNotesPerIdeabook(userTier: userTier);
});

/// Provider for the current user's daily chat limit
final chatLimitDailyProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getChatLimitDaily(userTier: userTier);
});

/// Provider for the current user's monthly chat limit
final chatLimitMonthlyProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getChatLimitMonthly(userTier: userTier);
});

/// Provider for the current user's audio recording length limit
final audioRecordingLengthSecondsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getAudioRecordingLengthSeconds(userTier: userTier);
});

/// Provider for the current user's ideabook name max words
final ideabookNameMaxWordsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getIdeabookNameMaxWords(userTier: userTier);
});

/// Provider for the current user's idea max words
final ideaMaxWordsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getIdeaMaxWords(userTier: userTier);
});

/// Provider for the current user's chat input max words
final chatInputMaxWordsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getChatInputMaxWords(userTier: userTier);
});

/// Provider for the current user's chat rate limits
final chatRateLimitsProvider = FutureProvider<List<ChatRateLimit>>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);

  final rateLimits = <ChatRateLimit>[];

  // Add rate limit per minute for all users
  final rateLimitMinute = appLimitsService.getChatRateLimitMinute(userTier: userTier);
  if (rateLimitMinute != null) {
    rateLimits.add(ChatRateLimit(
      maxMessages: rateLimitMinute,
      periodSeconds: 60,
      description: 'minute',
    ));
  }

  // Add daily limit
  final dailyLimit = appLimitsService.getChatLimitDaily(userTier: userTier);
  rateLimits.add(ChatRateLimit(
    maxMessages: dailyLimit,
    periodSeconds: 86400, // 24 hours in seconds
    description: 'day',
  ));

  // Add monthly limit
  final monthlyLimit = appLimitsService.getChatLimitMonthly(userTier: userTier);
  rateLimits.add(ChatRateLimit(
    maxMessages: monthlyLimit,
    periodSeconds: 2592000, // 30 days in seconds
    description: 'month',
  ));

  return rateLimits;
});
