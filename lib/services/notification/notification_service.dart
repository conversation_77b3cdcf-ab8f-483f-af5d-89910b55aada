import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/models/notification.dart';
import 'package:voji/utils/logger.dart';

/// Generic notification service for managing notifications
class NotificationService<T extends BaseNotification> {
  /// Show a notification
  /// Returns a function to dismiss the notification
  void Function(WidgetRef ref, T notification) show;

  /// Dismiss a notification
  void Function(WidgetRef ref, T notification) dismiss;

  /// Creates a new NotificationService
  NotificationService({
    required this.show,
    required this.dismiss,
  });

  /// Show a notification with auto-dismiss
  void showWithAutoDismiss(WidgetRef ref, T notification) {
    Logger.debug('NotificationService: Showing notification: ${notification.message}');

    // Show the notification
    show(ref, notification);

    // Schedule automatic dismissal
    Future.delayed(notification.autoDismissDuration, () {
      Logger.debug('NotificationService: Auto-dismissing notification: ${notification.message}');
      dismiss(ref, notification);
    });
  }
}

/// Factory for creating notification services
class NotificationServiceFactory {
  /// Create a notification service for ideabook notifications
  static NotificationService<IdeabookNotification> createIdeabookNotificationService() {
    return NotificationService<IdeabookNotification>(
      show: (ref, notification) {
        ref.read(ideabookNotificationProvider.notifier).state = notification;
      },
      dismiss: (ref, notification) {
        final currentNotification = ref.read(ideabookNotificationProvider);
        if (currentNotification != null &&
            currentNotification.ideabookId == notification.ideabookId &&
            currentNotification.createdAt == notification.createdAt) {
          ref.read(ideabookNotificationProvider.notifier).state = null;
        }
      },
    );
  }

  /// Create a notification service for bottom panel notifications
  static NotificationService<BottomPanelNotification> createBottomPanelNotificationService() {
    return NotificationService<BottomPanelNotification>(
      show: (ref, notification) {
        ref.read(bottomPanelNotificationProvider.notifier).state = notification;
      },
      dismiss: (ref, notification) {
        final currentNotification = ref.read(bottomPanelNotificationProvider);
        if (currentNotification != null &&
            currentNotification.createdAt == notification.createdAt) {
          ref.read(bottomPanelNotificationProvider.notifier).state = null;
        }
      },
    );
  }
}

/// Provider for the ideabook notification
final ideabookNotificationProvider = StateProvider<IdeabookNotification?>((ref) => null);

/// Provider for the bottom panel notification
final bottomPanelNotificationProvider = StateProvider<BottomPanelNotification?>((ref) => null);

/// Provider for the ideabook notification service
final ideabookNotificationServiceProvider = Provider<NotificationService<IdeabookNotification>>((ref) {
  return NotificationServiceFactory.createIdeabookNotificationService();
});

/// Provider for the bottom panel notification service
final bottomPanelNotificationServiceProvider = Provider<NotificationService<BottomPanelNotification>>((ref) {
  return NotificationServiceFactory.createBottomPanelNotificationService();
});

/// Provider function to show a notification for an ideabook
final showIdeabookNotificationProvider = Provider<void Function(WidgetRef, String, String, {Duration? duration})>((ref) {
  return (WidgetRef widgetRef, String ideabookId, String message, {Duration? duration}) {
    final notification = IdeabookNotification(
      ideabookId: ideabookId,
      message: message,
      createdAt: DateTime.now(),
      autoDismissDuration: duration ?? const Duration(seconds: 1, milliseconds: 500),
    );

    widgetRef.read(ideabookNotificationServiceProvider).showWithAutoDismiss(widgetRef, notification);
  };
});

/// Provider function to show a notification in the bottom panel
final showBottomPanelNotificationProvider = Provider<void Function(WidgetRef, String, {Duration? duration})>((ref) {
  return (WidgetRef widgetRef, String message, {Duration? duration}) {
    final notification = BottomPanelNotification(
      message: message,
      createdAt: DateTime.now(),
      autoDismissDuration: duration ?? const Duration(seconds: 1, milliseconds: 500),
    );

    widgetRef.read(bottomPanelNotificationServiceProvider).showWithAutoDismiss(widgetRef, notification);
  };
});
