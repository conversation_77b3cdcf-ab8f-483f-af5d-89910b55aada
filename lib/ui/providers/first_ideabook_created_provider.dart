import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/utils/logger.dart';

/// Provider that tracks when the first ideabook is created
/// This is used to trigger the first ideabook tour
final firstIdeabookCreatedProvider = StateNotifierProvider<FirstIdeabookCreatedNotifier, bool>((ref) {
  return FirstIdeabookCreatedNotifier();
});

/// Notifier for tracking when the first ideabook is created
class FirstIdeabookCreatedNotifier extends StateNotifier<bool> {
  FirstIdeabookCreatedNotifier() : super(false) {
    Logger.debug('Initializing FirstIdeabookCreatedNotifier with false');
  }

  /// Set the state to true when the first ideabook is created
  void setFirstIdeabookCreated() {
    Logger.debug('FirstIdeabookCreatedNotifier: Setting first ideabook created to true');
    state = true;
  }

  /// Reset the state to false
  void reset() {
    Logger.debug('FirstIdeabookCreatedNotifier: Resetting to false');
    state = false;
  }
}
