import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:voji/models/idea.dart';
import 'package:voji/models/ideabook.dart';
import 'package:voji/repositories/idea_repository.dart';
import 'package:voji/repositories/ideabook_repository.dart';
import 'package:voji/repositories/repository_providers.dart';
import 'package:voji/services/llm/gemini_chat_service.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/preferences/suggested_prompts_storage.dart';
import 'package:voji/services/remote_config/remote_config_providers.dart';
import 'package:voji/services/subscription/subscription_providers.dart';
import 'package:voji/utils/logger.dart';

/// State for suggested prompts
class SuggestedPromptsState {
  /// List of suggested prompts
  final List<String> prompts;

  /// Whether the prompts are loading
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  /// Constructor
  const SuggestedPromptsState({
    this.prompts = const [],
    this.isLoading = false,
    this.errorMessage,
  });

  /// Create a loading state
  factory SuggestedPromptsState.loading() {
    return const SuggestedPromptsState(isLoading: true);
  }

  /// Create a state with data
  factory SuggestedPromptsState.data(List<String> prompts) {
    return SuggestedPromptsState(prompts: prompts);
  }

  /// Create an error state
  factory SuggestedPromptsState.error(String message) {
    return SuggestedPromptsState(errorMessage: message);
  }

  /// Copy with new values
  SuggestedPromptsState copyWith({
    List<String>? prompts,
    bool? isLoading,
    String? errorMessage,
  }) {
    return SuggestedPromptsState(
      prompts: prompts ?? this.prompts,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  /// Clear the error message
  SuggestedPromptsState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// Notifier for suggested prompts
class SuggestedPromptsNotifier extends StateNotifier<SuggestedPromptsState> {
  final GeminiChatService _geminiChatService;
  final String _ideabookId;
  final IdeabookRepository _ideabookRepository;
  final IdeaRepository _ideaRepository;
  final LlmPrompts _llmPrompts;
  final Ref _ref;

  SuggestedPromptsNotifier(
    this._geminiChatService,
    this._ideabookRepository,
    this._ideaRepository,
    this._llmPrompts,
    this._ideabookId,
    this._ref,
  ) : super(SuggestedPromptsState.loading()) {
    _generateSuggestedPrompts();
  }

  /// Generate suggested prompts based on ideabook content
  Future<void> _generateSuggestedPrompts() async {
    try {
      Logger.debug('======= SUGGESTED PROMPTS DEBUGGING =======');
      Logger.debug('Generating suggested prompts for ideabook: $_ideabookId');

      // Only set to loading if not already loading
      if (!state.isLoading) {
        state = SuggestedPromptsState.loading();
      }

      // Get the user tier to check if generative prompts are enabled
      final userTier = await _ref.read(userTierProvider.future);
      Logger.debug('User tier: $userTier');

      // Check if generative suggested prompts are enabled for this user tier
      final isGenerativeEnabled = _llmPrompts.isGenerativeSuggestedPromptsEnabled(userTier: userTier);
      Logger.debug('Generative suggested prompts enabled: $isGenerativeEnabled');

      // If generative prompts are disabled, use fallback prompts
      if (!isGenerativeEnabled) {
        Logger.debug('Using fallback suggested prompts');
        final fallbackPrompts = _getFallbackPrompts();
        state = SuggestedPromptsState.data(fallbackPrompts);
        Logger.debug('Updated state with fallback prompts: $fallbackPrompts');
        Logger.debug('======= END SUGGESTED PROMPTS DEBUGGING =======');
        return;
      }

      // Get the ideabook
      final ideabook = await _ideabookRepository.getIdeabookById(_ideabookId);

      if (ideabook == null) {
        Logger.error('Ideabook not found: $_ideabookId');
        state = SuggestedPromptsState.error('Ideabook not found');
        return;
      }

      Logger.debug('Found ideabook: ${ideabook.name}');

      // Get the ideas for this ideabook
      final ideas = await _ideaRepository.getIdeasByIdeabookId(_ideabookId);
      Logger.debug('Retrieved ${ideas.length} ideas for the ideabook');

      // Format the ideas for the prompt
      final formattedIdeas = _formatIdeasForPrompt(ideas);
      Logger.debug('Formatted ideas for prompt:');
      Logger.debug(formattedIdeas);

      // Load previous suggestions
      final previousSuggestions = await SuggestedPromptsStorage.loadSuggestedPrompts(_ideabookId);
      Logger.debug('Loaded ${previousSuggestions.length} previous suggestions');

      // Create the prompt for Gemma
      final promptContent = _createPromptForGemma(ideabook, formattedIdeas, previousSuggestions);
      Logger.debug('Generated prompt for Gemma:');
      Logger.debug(promptContent);

      // Call Gemini API
      Logger.debug('Calling Gemini API to generate suggested prompts...');
      final response = await _geminiChatService.generateSuggestedPrompts(promptContent);

      if (response.isSuccess && response.content != null) {
        Logger.debug('Successfully received response from Gemini API');
        Logger.debug('Raw response content: ${response.content}');

        // Parse the JSON response - first check if we have originalJson
        List<String> suggestedPrompts;
        if (response.originalJson != null && response.originalJson!.containsKey('prompts')) {
          // Use the already parsed JSON from the GemmaService
          Logger.debug('Using originalJson from GemmaService');
          final promptsList = response.originalJson!['prompts'] as List<dynamic>?;

          if (promptsList != null) {
            suggestedPrompts = promptsList
                .map((item) => (item['prompt'] as String?) ?? '')
                .where((prompt) => prompt.isNotEmpty)
                .toList();

            Logger.debug('Parsed ${suggestedPrompts.length} prompts from originalJson');
          } else {
            // Fall back to parsing the content
            Logger.debug('originalJson does not contain valid prompts list, falling back to content parsing');
            suggestedPrompts = _parseSuggestedPrompts(response.content!);
          }
        } else {
          // Parse the content
          Logger.debug('No originalJson available, parsing content');
          suggestedPrompts = _parseSuggestedPrompts(response.content!);
        }

        Logger.debug('Parsed suggested prompts: $suggestedPrompts');

        // Save the new suggestions to storage
        if (suggestedPrompts.isNotEmpty) {
          await SuggestedPromptsStorage.saveSuggestedPrompts(_ideabookId, suggestedPrompts);
          Logger.debug('Saved ${suggestedPrompts.length} new suggestions to storage');
        }

        // Update the state with the suggested prompts
        state = SuggestedPromptsState.data(suggestedPrompts);
        Logger.debug('Updated state with suggested prompts');
      } else {
        // Handle error
        Logger.error('Error from Gemini API', response.errorMessage);
        state = SuggestedPromptsState.error(
          response.errorMessage ?? 'Failed to generate suggestions'
        );
      }

      Logger.debug('======= END SUGGESTED PROMPTS DEBUGGING =======');
    } catch (e) {
      Logger.error('Error generating suggested prompts', e);
      state = SuggestedPromptsState.error('Error generating suggestions: $e');
    }
  }

  /// Format ideas for the prompt
  String _formatIdeasForPrompt(List<Idea> ideas) {
    if (ideas.isEmpty) {
      return '';
    }

    // Format date only (no time)
    final dateFormat = DateFormat('yyyy-MM-dd');

    // Sort ideas by creation date (newest first)
    final sortedIdeas = List<Idea>.from(ideas)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // Format each idea as a bullet point with date (using local timezone)
    return sortedIdeas.map((idea) {
      final dateStr = dateFormat.format(idea.createdAt.toLocal());
      return '* $dateStr | ${idea.content}';
    }).join('\n');
  }

  /// Create the prompt for Gemma
  String _createPromptForGemma(Ideabook ideabook, String formattedIdeas, [List<String> previousSuggestions = const []]) {
    // Format previous suggestions if any
    String previousSuggestionsText = '';
    if (previousSuggestions.isNotEmpty) {
      previousSuggestionsText = '''
Here are some prompts you suggested before. You can still suggest them again if they are still relevant. But consider suggesting new prompts that are more relevant to the ideabook.
${previousSuggestions.map((prompt) => '* $prompt').join('\n')}
''';
    }

    // Get the suggested prompts prompt template from Remote Config and replace variables
    final promptTemplate = _llmPrompts.getSuggestedPromptsPrompt();
    return _llmPrompts.replaceVariables(promptTemplate, {
      'ideabook_name': ideabook.name,
      'formatted_ideas': formattedIdeas,
      'previous_suggestions_text': previousSuggestionsText,
    });
  }

  /// Parse the suggested prompts from the JSON response
  List<String> _parseSuggestedPrompts(String jsonContent) {
    // Check if we already have parsed JSON from the GemmaService
    if (jsonContent.contains('"prompts"') && jsonContent.contains('"prompt"')) {
      try {
        // First attempt: Try to parse the JSON directly
        final jsonData = json.decode(jsonContent);

        // Extract the prompts from the JSON
        final promptsList = jsonData['prompts'] as List<dynamic>?;

        if (promptsList != null) {
          final prompts = promptsList
              .map((item) => (item['prompt'] as String?) ?? '')
              .where((prompt) => prompt.isNotEmpty)
              .toList();

          if (prompts.isNotEmpty) {
            Logger.debug('Successfully parsed ${prompts.length} prompts from direct JSON');
            return prompts;
          }
        }
      } catch (e) {
        Logger.debug('First attempt to parse JSON failed: $e');
      }

      // Second attempt: Try to extract JSON from the text
      try {
        // Try to find JSON in the response
        final jsonRegex = RegExp(r'{[\s\S]*?}');
        final matches = jsonRegex.allMatches(jsonContent);

        for (final match in matches) {
          try {
            final jsonStr = match.group(0)!;
            final jsonData = json.decode(jsonStr);

            if (jsonData.containsKey('prompts')) {
              final promptsList = jsonData['prompts'] as List<dynamic>?;

              if (promptsList != null) {
                final prompts = promptsList
                    .map((item) => (item['prompt'] as String?) ?? '')
                    .where((prompt) => prompt.isNotEmpty)
                    .toList();

                if (prompts.isNotEmpty) {
                  Logger.debug('Successfully parsed ${prompts.length} prompts from extracted JSON');
                  return prompts;
                }
              }
            }
          } catch (e) {
            Logger.debug('Failed to parse extracted JSON match: $e');
            // Continue to the next match
          }
        }
      } catch (e) {
        Logger.debug('Second attempt to extract JSON failed: $e');
      }

      // Third attempt: Try to clean up the JSON string
      try {
        String cleanedJson = jsonContent
            .replaceAll(RegExp(r'[\n\r]'), ' ')  // Remove newlines
            .replaceAll(RegExp(r'\s+'), ' ')     // Normalize whitespace
            .trim();

        // Find the start and end of what looks like a JSON object
        int startIdx = cleanedJson.indexOf('{');
        int endIdx = cleanedJson.lastIndexOf('}') + 1;

        if (startIdx >= 0 && endIdx > startIdx) {
          cleanedJson = cleanedJson.substring(startIdx, endIdx);

          // Try to parse the cleaned JSON
          final jsonData = json.decode(cleanedJson);

          if (jsonData.containsKey('prompts')) {
            final promptsList = jsonData['prompts'] as List<dynamic>?;

            if (promptsList != null) {
              final prompts = promptsList
                  .map((item) => (item['prompt'] as String?) ?? '')
                  .where((prompt) => prompt.isNotEmpty)
                  .toList();

              if (prompts.isNotEmpty) {
                Logger.debug('Successfully parsed ${prompts.length} prompts from cleaned JSON');
                return prompts;
              }
            }
          }
        }
      } catch (e) {
        Logger.debug('Third attempt to clean and parse JSON failed: $e');
      }

      // Fourth attempt: Try to extract prompts using regex
      try {
        final promptRegex = RegExp(r'"prompt"\s*:\s*"([^"]+)"');
        final matches = promptRegex.allMatches(jsonContent);

        if (matches.isNotEmpty) {
          final prompts = matches
              .map((match) => match.group(1) ?? '')
              .where((prompt) => prompt.isNotEmpty)
              .toList();

          if (prompts.isNotEmpty) {
            Logger.debug('Successfully extracted ${prompts.length} prompts using regex');
            return prompts;
          }
        }
      } catch (e) {
        Logger.debug('Fourth attempt to extract prompts using regex failed: $e');
      }
    }

    // If all parsing attempts fail, log the error and return default prompts
    Logger.error('All attempts to parse prompts from JSON failed: $jsonContent');
    return _getDefaultPrompts();
  }

  /// Get fallback prompts when generative prompts are disabled
  List<String> _getFallbackPrompts() {
    return [
      "What is the TL;DR of this ideabook?",
      "Summarize the ideas into a list of action items",
      "Write a story based on the ideas",
      "List the ideas in a bullet point list in reverse-chronological order",
      "Help me brainstorm more ideas",
    ];
  }

  /// Get default prompts if generation fails
  List<String> _getDefaultPrompts() {
    return _getFallbackPrompts();
  }

  /// Refresh the suggested prompts
  Future<void> refresh() async {
    Logger.debug('Refreshing suggested prompts for ideabook: $_ideabookId');

    // Always clear existing prompts and set to loading state
    state = SuggestedPromptsState.loading();

    // Generate new prompts
    await _generateSuggestedPrompts();
  }

  /// Clear the error message
  void clearError() {
    state = state.clearError();
  }
}

/// Provider for the suggested prompts notifier
final suggestedPromptsProvider = StateNotifierProvider.family<SuggestedPromptsNotifier, SuggestedPromptsState, String>((ref, ideabookId) {
  final geminiChatService = ref.watch(geminiChatServiceProvider);
  final ideabookRepository = ref.watch(ideabookRepositoryProvider);
  final ideaRepository = ref.watch(ideaRepositoryProvider);
  final llmPrompts = ref.watch(llmPromptsProvider);

  return SuggestedPromptsNotifier(
    geminiChatService,
    ideabookRepository,
    ideaRepository,
    llmPrompts,
    ideabookId,
    ref,
  );
});
