import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:voji/models/models.dart' hide BottomPanelNotification;
import 'package:voji/repositories/repository_providers.dart';
import 'package:voji/services/audio/audio_providers.dart';
import 'package:voji/services/audio/idea_recording_controller.dart';
import 'package:voji/services/limits/app_limits_providers.dart';
import 'package:voji/ui/providers/bottom_panel_notification_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/advanced_audio_recording_panel.dart';
import 'package:voji/utils/logger.dart';

/// Enum representing the state of the ideabook detail bottom panel
enum IdeabookDetailBottomPanelState {
  /// Normal state showing the "New Idea" button
  normal,

  /// Recording state showing the audio recording panel
  recording,

  /// Notification state showing a notification message
  notification,
}

/// Provider to track the state of the ideabook detail bottom panel
final ideabookDetailBottomPanelStateProvider =
    StateProvider<IdeabookDetailBottomPanelState>(
      (ref) => IdeabookDetailBottomPanelState.normal,
    );

/// Widget that controls the bottom panel of the ideabook detail screen
class IdeabookDetailBottomPanelController extends ConsumerStatefulWidget {
  /// The ideabook ID
  final String ideabookId;

  /// Constructor
  const IdeabookDetailBottomPanelController({
    super.key,
    required this.ideabookId,
  });

  @override
  ConsumerState<IdeabookDetailBottomPanelController> createState() =>
      _IdeabookDetailBottomPanelControllerState();
}

class _IdeabookDetailBottomPanelControllerState
    extends ConsumerState<IdeabookDetailBottomPanelController> {
  @override
  Widget build(BuildContext context) {
    final panelState = ref.watch(ideabookDetailBottomPanelStateProvider);
    final notification = ref.watch(bottomPanelNotificationProvider);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: _buildPanel(context, panelState, notification),
    );
  }

  /// Build the appropriate panel based on the current state
  Widget _buildPanel(
    BuildContext context,
    IdeabookDetailBottomPanelState state,
    BottomPanelNotification? notification,
  ) {
    switch (state) {
      case IdeabookDetailBottomPanelState.normal:
        return _buildNewIdeaButton(context);
      case IdeabookDetailBottomPanelState.recording:
        return _buildRecordingPanel(context);
      case IdeabookDetailBottomPanelState.notification:
        if (notification != null) {
          return _buildNotificationPanel(context, notification);
        } else {
          // Fallback if notification is null but we're in notification state
          return _buildNewIdeaButton(context);
        }
    }
  }

  // Fixed height for the bottom panel to maintain consistency across all states
  static const double kBottomPanelHeight = 80.0;

  /// Build the "New Idea" button
  Widget _buildNewIdeaButton(BuildContext context) {
    return SizedBox(
      key: const ValueKey('new-idea-button'),
      height: kBottomPanelHeight,
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 200),
          child: OutlinedButton.icon(
            onPressed: () async {
              // Check if the ideabook is full
              final ideaRepository = ref.read(ideaRepositoryProvider);
              final isFull = await ideaRepository.isIdeabookFull(
                widget.ideabookId,
              );

              if (isFull) {
                // Get the max ideas limit from the provider for logging
                try {
                  final maxIdeas = await ref.read(
                    maxIdeasPerIdeabookProvider.future,
                  );
                  Logger.debug('Ideabook is full (limit: $maxIdeas), showing dialog');
                } catch (e) {
                  Logger.error('Failed to get max ideas limit for logging', e);
                }

                // Show error dialog if the ideabook is full
                if (mounted) {
                  // Use a post-frame callback to show the dialog after the current frame is complete
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      showDialog(
                        context: context,
                        builder:
                            (dialogContext) => AlertDialog(
                              title: Text(
                                'Ideabook Full! 🈵',
                                style:
                                    VojiTheme.textStylesOf(
                                      dialogContext,
                                    ).bodyLarge,
                              ),
                              content: Text(
                                "Wow, you've packed this Ideabook with many amazing ideas! 🤩 Time for a new one 📖 to keep those brilliant thoughts flowing. Create a fresh Ideabook to add more! 💡",
                                style:
                                    VojiTheme.textStylesOf(
                                      dialogContext,
                                    ).bodyMedium,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.zero,
                                side: BorderSide(
                                  color:
                                      VojiTheme.colorsOf(dialogContext).border,
                                  width: 1,
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(dialogContext).pop(),
                                  child: Text(
                                    'OK',
                                    style:
                                        VojiTheme.textStylesOf(
                                          dialogContext,
                                        ).buttonText,
                                  ),
                                ),
                              ],
                            ),
                      );
                    }
                  });
                }
                return;
              }

              // Request microphone permission before entering recording mode
              final recordingService = ref.read(audioRecordingServiceProvider);

              // Check if permission is already granted
              final hasPermission = await recordingService.checkPermission();
              if (hasPermission) {
                // Enter recording mode
                ref
                    .read(ideabookDetailBottomPanelStateProvider.notifier)
                    .state = IdeabookDetailBottomPanelState.recording;
                return;
              }

              // Request permission
              final status = await recordingService.requestPermission();
              if (status.isGranted) {
                // Enter recording mode
                ref
                    .read(ideabookDetailBottomPanelStateProvider.notifier)
                    .state = IdeabookDetailBottomPanelState.recording;
                return;
              }

              // If permission is denied, show settings dialog
              if (mounted) {
                // Use a post-frame callback to show the dialog after the current frame is complete
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    showDialog(
                      context: context,
                      builder:
                          (dialogContext) => AlertDialog(
                            title: Text(
                              'Microphone Permission Required',
                              style:
                                  VojiTheme.textStylesOf(
                                    dialogContext,
                                  ).bodyLarge,
                            ),
                            content: Text(
                              'Microphone permission is required to record audio. '
                              'Please enable it in app settings.',
                              style:
                                  VojiTheme.textStylesOf(
                                    dialogContext,
                                  ).bodyMedium,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.zero,
                              side: BorderSide(
                                color: VojiTheme.colorsOf(dialogContext).border,
                                width: 1,
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed:
                                    () => Navigator.of(dialogContext).pop(),
                                child: Text(
                                  'Cancel',
                                  style:
                                      VojiTheme.textStylesOf(
                                        dialogContext,
                                      ).buttonText,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  Navigator.of(dialogContext).pop();
                                  openAppSettings();
                                },
                                child: Text(
                                  'Open Settings',
                                  style:
                                      VojiTheme.textStylesOf(
                                        dialogContext,
                                      ).buttonText,
                                ),
                              ),
                            ],
                          ),
                    );
                  }
                });
              }
            },
            icon: const Icon(Icons.mic),
            label: Text(
              'New Idea',
              style: VojiTheme.textStylesOf(context).buttonText,
            ),
            style: Theme.of(context).outlinedButtonTheme.style,
          ),
        ),
      ),
    );
  }

  /// Build the recording panel
  Widget _buildRecordingPanel(BuildContext context) {
    return SizedBox(
      height: kBottomPanelHeight,
      child: AdvancedAudioRecordingPanel(
        key: const ValueKey('recording-panel'),
        layout: AudioRecordingPanelLayout.horizontal,
        showTimer: false,
        onRecordingCompleted: _handleRecordingCompleted,
        onRecordingFailed: _handleRecordingFailed,
        onRecordingCancelled: _handleRecordingCancelled,
        onPermissionDenied: _handlePermissionDenied,
      ),
    );
  }

  /// Build the notification panel
  Widget _buildNotificationPanel(
    BuildContext context,
    BottomPanelNotification notification,
  ) {
    return SizedBox(
      key: const ValueKey('notification-panel'),
      height: kBottomPanelHeight,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Notification message
            Expanded(
              child: Text(
                notification.message,
                style: VojiTheme.textStylesOf(
                  context,
                ).bodyLarge.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Check icon to indicate success
            Icon(
              Icons.check_circle,
              color: VojiTheme.getIdeabookColor(
                context,
                IdeabookColor.green.index,
              ),
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  /// Handle recording completion
  Future<void> _handleRecordingCompleted(
    String filePath,
    Duration duration,
  ) async {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the recording completion
    await controller.handleRecordingCompleted(ref, filePath, duration);

    // Switch to notification state
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.notification;

    // Schedule returning to normal state after notification is shown
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
            IdeabookDetailBottomPanelState.normal;
      }
    });
  }

  /// Handle recording cancellation
  void _handleRecordingCancelled() {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the recording cancellation
    controller.handleRecordingCancelled(ref);

    // Return to normal state
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.normal;
  }

  /// Handle recording failure
  void _handleRecordingFailed(String errorMessage) {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the recording failure
    controller.handleRecordingFailed(ref, errorMessage);

    // Switch to notification state
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.notification;

    // Schedule returning to normal state after notification is shown
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
            IdeabookDetailBottomPanelState.normal;
      }
    });
  }

  /// Handle permission denial
  void _handlePermissionDenied() {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the permission denial
    controller.handlePermissionDenied(ref);

    // Return to normal state
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.normal;
  }
}
