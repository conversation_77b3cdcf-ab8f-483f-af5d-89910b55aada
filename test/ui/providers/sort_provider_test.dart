import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voji/ui/providers/sort_provider.dart';

void main() {
  group('SortOrderNotifier', () {
    late ProviderContainer container;

    setUp(() async {
      // Set up shared preferences for testing
      SharedPreferences.setMockInitialValues({});

      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with the default sort order', () async {
      // Act
      final sortOrder = container.read(ideabookSortOrderProvider);

      // Assert
      expect(sortOrder, SortOrder.ascending);
    });

    test('should toggle sort order and persist the change', () async {
      // Act - toggle the sort order
      container.read(ideabookSortOrderProvider.notifier).toggleSortOrder();

      // Assert - check the provider state
      expect(container.read(ideabookSortOrderProvider), SortOrder.descending);

      // Assert - check that the value was persisted
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getString('voji_ideabook_sort_order'), 'descending');
    });

    test('should set sort order explicitly and persist the change', () async {
      // Act - set the sort order explicitly
      container.read(ideabookSortOrderProvider.notifier).setSortOrder(SortOrder.descending);

      // Assert - check the provider state
      expect(container.read(ideabookSortOrderProvider), SortOrder.descending);

      // Assert - check that the value was persisted
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getString('voji_ideabook_sort_order'), 'descending');
    });

    test('should load saved sort order from storage on initialization', () async {
      // Arrange - save a sort order to storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('voji_ideabook_sort_order', 'descending');

      // Create a custom provider to track when the initialization is complete
      final initializationCompleteProvider = StateProvider<bool>((ref) => false);

      // Create a provider that depends on the sort order provider and marks initialization as complete
      final testProvider = Provider<void>((ref) {
        ref.listen(ideabookSortOrderProvider, (previous, next) {
          // When the sort order changes from the initial value, mark initialization as complete
          if (previous != next) {
            ref.read(initializationCompleteProvider.notifier).state = true;
          }
        });
      });

      // Act - create a new container with our test providers
      final newContainer = ProviderContainer();

      // Initialize our test provider
      newContainer.read(testProvider);

      // Wait for the initialization to complete or timeout after 1 second
      int attempts = 0;
      while (!newContainer.read(initializationCompleteProvider) && attempts < 10) {
        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
      }

      // Assert - check that the provider loaded the saved value
      expect(newContainer.read(ideabookSortOrderProvider), SortOrder.descending);

      // Clean up
      newContainer.dispose();
    });
  });
}
